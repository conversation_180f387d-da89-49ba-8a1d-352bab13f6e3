'use client';

import {
  R<PERSON><PERSON>lient<PERSON>rovider,
  useRTVI<PERSON>lient,
  useRTVIClientTransportState,
  RTVIClientAudio,
} from '@pipecat-ai/client-react';
import { RTVIClient } from '@pipecat-ai/client-js';
import { DailyTransport } from '@pipecat-ai/daily-transport';
import { useEffect, useState, useMemo } from 'react';
import dynamic from 'next/dynamic';

// 1. Fixed AIVoiceInput dynamic import
const AIVoiceInput = dynamic(
  () => import('./ui/ai-voice-input').then(mod => mod.AIVoiceInput),
  {
    ssr: false,
    loading: () => <div>Loading microphone...</div>
  }
);

interface PipecatWidgetProps {
  agentId?: string;
  testMode?: boolean;
}

export function PipecatWidget({ agentId, testMode = false }: PipecatWidgetProps) {
  // Create client with agent-specific configuration
  // Use useMemo to recreate client when agentId changes
  const client = useMemo(() => {
    return new RTVIClient({
      transport: new DailyTransport(),
      params: {
        baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:7860',
        endpoints: {
          connect: '/connect',
          action: '/action'
        },
        requestData: agentId ? { agent_id: agentId } : undefined
      },
      enableMic: true,
      enableCam: false
    });
  }, [agentId]);

  return (
    <RTVIClientProvider client={client}>
      <WidgetContent testMode={testMode} agentId={agentId} />
    </RTVIClientProvider>
  );
}

interface WidgetContentProps {
  testMode?: boolean;
  agentId?: string;
}

function WidgetContent({ testMode = false, agentId }: WidgetContentProps) {
  const client = useRTVIClient();
  const transportState = useRTVIClientTransportState();
  const [error, setError] = useState<string | null>(null);
  const [isMounted, setIsMounted] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    return () => {
      if (client && ['connected', 'ready'].includes(transportState)) {
        client.disconnect();
      }
    };
  }, [client, transportState]);

  if (!isMounted) return null;

  const containerClass = testMode
    ? "flex flex-col items-center gap-4"
    : "fixed bottom-8 right-8 flex flex-col items-end gap-4";

  const isConnected = ['connected', 'ready'].includes(transportState);
  const canConnect = agentId || testMode;

  const handleToggle = async (isActive: boolean) => {
    if (isConnecting) return; // Prevent multiple connection attempts

    try {
      setError(null);

      if (isActive) {
        if (!canConnect) {
          setError('Please select an agent to start conversation');
          return;
        }

        setIsConnecting(true);
        await client.connect();
      } else {
        await client.disconnect();
      }
    } catch (err) {
      console.error('Connection error:', err);
      setError(
        err instanceof Error ? err.message : 'Connection failed'
      );
    } finally {
      setIsConnecting(false);
    }
  };

  return (
    <div className={containerClass}>
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg">
        {error && (
          <div className="text-red-500 text-sm p-2 max-w-xs">
            {error}
          </div>
        )}
        {!canConnect && (
          <div className="text-yellow-600 text-sm p-2 max-w-xs">
            No agent selected. Please select an agent to start conversation.
          </div>
        )}
        {isConnecting && (
          <div className="text-blue-600 text-sm p-2">
            Connecting...
          </div>
        )}
        <AIVoiceInput
          isActive={isConnected}
          onChange={handleToggle}
          className="w-auto"
          demoMode={false}
          disabled={!canConnect || isConnecting}
        />
      </div>
      <RTVIClientAudio />
    </div>
  );
}