# utils/agent_model.py
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field

# --- Agent Data Model ---
class Agent(BaseModel):
    id: Optional[str] = None
    name: str
    description: Optional[str] = None
    type: str = Field(..., pattern="^(simple|flow)$")
    configuration: Optional[Dict[str, Any]] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    stt_service: Optional[str] = None
    llm_service: Optional[str] = None
    tts_service: Optional[str] = None
    tts_config: Optional[Dict[str, Any]] = None
    stt_config: Optional[Dict[str, Any]] = None
    llm_config: Optional[Dict[str, Any]] = None
    vad_config: Optional[Dict[str, Any]] = None

    class Config:
        orm_mode = True