{"name": "client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@pipecat-ai/client-js": "^0.3.2", "@pipecat-ai/client-react": "^0.3.2", "@pipecat-ai/daily-transport": "^0.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.473.0", "next": "15.1.5", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}