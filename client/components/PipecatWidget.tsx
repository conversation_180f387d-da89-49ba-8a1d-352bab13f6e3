'use client';

import {
  R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  useRTVI<PERSON>lient,
  useRTVIClientTransportState,
  RTVIClientAudio,
} from '@pipecat-ai/client-react';
import { RTVIClient, RTVIEvent } from '@pipecat-ai/client-js';
import { DailyTransport } from '@pipecat-ai/daily-transport';
import { useEffect, useState, useMemo, useRef } from 'react';
import dynamic from 'next/dynamic';

// 1. Fixed AIVoiceInput dynamic import
const AIVoiceInput = dynamic(
  () => import('./ui/ai-voice-input').then(mod => mod.AIVoiceInput),
  {
    ssr: false,
    loading: () => <div>Loading microphone...</div>
  }
);

interface PipecatWidgetProps {
  agentId?: string;
  testMode?: boolean;
}

export function PipecatWidget({ agentId, testMode = false }: PipecatWidgetProps) {
  // Create client with agent-specific configuration
  // Use useMemo to recreate client when agentId changes
  const client = useMemo(() => {
    if (!agentId && !testMode) return null;

    console.log('PipecatWidget: Creating RTVI client for agent:', agentId);

    const rtviClient = new RTVIClient({
      transport: new DailyTransport(),
      params: {
        baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:7860',
        endpoints: {
          connect: '/connect',
          action: '/action'
        },
        requestData: agentId ? { agent_id: agentId } : undefined
      },
      enableMic: true,
      enableCam: false,
      callbacks: {
        onConnected: () => {
          console.log('PipecatWidget: RTVI Client connected');
        },
        onDisconnected: () => {
          console.log('PipecatWidget: RTVI Client disconnected');
        },
        onTransportStateChanged: (state) => {
          console.log('PipecatWidget: Transport state changed:', state);
        },
        onBotReady: (data) => {
          console.log('PipecatWidget: Bot ready:', data);
        },
        onUserTranscript: (data) => {
          if (data.final) {
            console.log('PipecatWidget: User said:', data.text);
          }
        },
        onBotTranscript: (data) => {
          console.log('PipecatWidget: Bot said:', data.text);
        },
        onError: (error) => {
          console.error('PipecatWidget: RTVI Client error:', error);
        }
      }
    });

    return rtviClient;
  }, [agentId, testMode]);

  if (!client) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="text-sm text-muted-foreground">
          {!agentId && !testMode ? 'No agent selected' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (
    <RTVIClientProvider client={client}>
      <WidgetContent testMode={testMode} agentId={agentId} />
    </RTVIClientProvider>
  );
}

interface WidgetContentProps {
  testMode?: boolean;
  agentId?: string;
}

function WidgetContent({ testMode = false, agentId }: WidgetContentProps) {
  const client = useRTVIClient();
  const transportState = useRTVIClientTransportState();
  const [error, setError] = useState<string | null>(null);
  const [isMounted, setIsMounted] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const initializationRef = useRef(false);

  // Initialize audio element for bot audio
  useEffect(() => {
    if (!audioRef.current) {
      audioRef.current = document.createElement('audio');
      audioRef.current.autoplay = true;
      audioRef.current.playsInline = true;
      document.body.appendChild(audioRef.current);
    }

    return () => {
      if (audioRef.current && document.body.contains(audioRef.current)) {
        // Clean up audio tracks before removing element
        if (audioRef.current.srcObject) {
          const tracks = audioRef.current.srcObject.getTracks();
          tracks.forEach(track => track.stop());
          audioRef.current.srcObject = null;
        }
        document.body.removeChild(audioRef.current);
        audioRef.current = null;
      }
    };
  }, []);

  // Setup audio track (same as SimpleVoiceWidget)
  const setupAudioTrack = (track: MediaStreamTrack) => {
    if (!audioRef.current) return;

    console.log('PipecatWidget: Setting up audio track');

    // Check if we're already playing this track
    if (audioRef.current.srcObject) {
      const oldTrack = audioRef.current.srcObject.getAudioTracks()[0];
      if (oldTrack?.id === track.id) return;
    }

    // Create a new MediaStream with the track and set it as the audio source
    audioRef.current.srcObject = new MediaStream([track]);
  };

  // Setup media tracks (same as SimpleVoiceWidget)
  const setupMediaTracks = () => {
    if (!client) return;

    const tracks = client.tracks();
    if (tracks.bot?.audio) {
      setupAudioTrack(tracks.bot.audio);
    }
  };

  // Initialize client and set up event listeners (same as SimpleVoiceWidget)
  useEffect(() => {
    if (!client || isInitialized || initializationRef.current) return;

    const initializeClient = async () => {
      try {
        initializationRef.current = true;
        console.log('PipecatWidget: Initializing RTVI client...');

        // Initialize devices (CRITICAL - this was missing!)
        await client.initDevices();

        // Set up track listeners (same as SimpleVoiceWidget)
        client.on(RTVIEvent.TrackStarted, (track, participant) => {
          console.log('PipecatWidget: Track started:', track.kind, participant?.name);
          if (!participant?.local && track.kind === 'audio') {
            setupAudioTrack(track);
          }
        });

        client.on(RTVIEvent.TrackStopped, (track, participant) => {
          console.log('PipecatWidget: Track stopped:', track.kind, participant?.name);
        });

        setIsInitialized(true);
        console.log('PipecatWidget: RTVI client initialized successfully');
      } catch (err) {
        console.error('PipecatWidget: Failed to initialize client:', err);
        setError('Failed to initialize audio devices');
        initializationRef.current = false;
      }
    };

    initializeClient();

    // Cleanup only when component unmounts or client changes (same as SimpleVoiceWidget)
    return () => {
      if (client) {
        console.log('PipecatWidget: Cleaning up - disconnecting client');
        client.disconnect().catch(console.error);
      }
    };
  }, [client]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Handle transport state changes (setup media when ready)
  useEffect(() => {
    if (transportState === 'ready') {
      console.log('PipecatWidget: Transport ready, setting up media tracks');
      setupMediaTracks();
    }
  }, [transportState]);

  if (!isMounted || !isInitialized) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="text-sm text-muted-foreground">
          Initializing audio devices...
        </div>
      </div>
    );
  }

  const containerClass = testMode
    ? "flex flex-col items-center gap-4"
    : "fixed bottom-8 right-8 flex flex-col items-end gap-4";

  const isConnected = ['connected', 'ready'].includes(transportState);
  const canConnect = (agentId || testMode) && isInitialized;

  const handleToggle = async (isActive: boolean) => {
    if (isConnecting) return; // Prevent multiple connection attempts

    try {
      setError(null);

      if (isActive) {
        if (!canConnect) {
          setError('Please select an agent to start conversation');
          return;
        }

        console.log('PipecatWidget: Starting connection...');
        setIsConnecting(true);

        // Connect to the bot (same as SimpleVoiceWidget)
        await client.connect();
        console.log('PipecatWidget: Connection successful');
      } else {
        console.log('PipecatWidget: Disconnecting...');
        await client.disconnect();

        // Clean up audio (same as SimpleVoiceWidget)
        if (audioRef.current?.srcObject) {
          const tracks = audioRef.current.srcObject.getTracks();
          tracks.forEach(track => track.stop());
          audioRef.current.srcObject = null;
        }

        console.log('PipecatWidget: Disconnected successfully');
      }
    } catch (err) {
      console.error('PipecatWidget: Connection error:', err);
      setError(
        err instanceof Error ? err.message : 'Connection failed'
      );
    } finally {
      setIsConnecting(false);
    }
  };

  return (
    <div className={containerClass}>
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg">
        {error && (
          <div className="text-red-500 text-sm p-2 max-w-xs">
            {error}
          </div>
        )}
        {!canConnect && (
          <div className="text-yellow-600 text-sm p-2 max-w-xs">
            {!agentId && !testMode
              ? 'No agent selected. Please select an agent to start conversation.'
              : 'Initializing audio devices...'
            }
          </div>
        )}
        {isConnecting && (
          <div className="text-blue-600 text-sm p-2">
            Connecting to agent...
          </div>
        )}
        {transportState && (
          <div className="text-xs text-gray-500 p-2">
            Status: {transportState}
          </div>
        )}
        <AIVoiceInput
          isActive={isConnected}
          onChange={handleToggle}
          className="w-auto"
          demoMode={false}
          disabled={!canConnect || isConnecting}
        />
      </div>
      <RTVIClientAudio />
    </div>
  );
}