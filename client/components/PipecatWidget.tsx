'use client';

import {
  RTV<PERSON>lientProvider,
  useRTVIClient,
  useRTVIClientTransportState,
  RTVIClientAudio,
} from '@pipecat-ai/client-react';
import { RTVIClient } from '@pipecat-ai/client-js';
import { DailyTransport } from '@pipecat-ai/daily-transport';
import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// 1. Fixed AIVoiceInput dynamic import
const AIVoiceInput = dynamic(
  () => import('./ui/ai-voice-input').then(mod => mod.AIVoiceInput),
  { 
    ssr: false,
    loading: () => <div>Loading microphone...</div>
  }
);

// Client initialization
const client = new RTVIClient({
  transport: new DailyTransport(),
  params: {
    baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:7860',
    endpoints: {
      connect: '/connect',
      action: '/action'
    }
  },
  enableMic: true,
  enableCam: false
});

export function PipecatWidget() {
  return (
    // 2. Added client prop to RTVIClientProvider
    <RTVIClientProvider client={client}>
      <WidgetContent />
    </RTVIClientProvider>
  );
}

function WidgetContent() {
  const client = useRTVIClient();
  const transportState = useRTVIClientTransportState();
  const [error, setError] = useState<string | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    return () => {
      client?.disconnect();
    };
  }, [client]);

  if (!isMounted) return null;

  return (
    <div className="fixed bottom-8 right-8 flex flex-col items-end gap-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg">
        {error && (
          <div className="text-red-500 text-sm p-2">{error}</div>
        )}
        {/* 3. Fixed AIVoiceInput props */}
        <AIVoiceInput
          isActive={['connected', 'ready'].includes(transportState)}
          onChange={async (isActive: boolean) => {
            try {
              if (isActive) {
                await client?.connect();
              } else {
                await client?.disconnect();
              }
              setError(null);
            } catch (err) {
              setError(
                err instanceof Error ? err.message : 'Connection failed'
              );
            }
          }}
          className="w-auto"
          demoMode={false}
        />
      </div>
      <RTVIClientAudio />
    </div>
  );
}