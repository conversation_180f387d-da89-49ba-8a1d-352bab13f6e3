'use client';

import {
  R<PERSON><PERSON><PERSON><PERSON>rov<PERSON>,
  useRTVI<PERSON><PERSON>,
  useRTVIClientTransportState,
  RTVIClientAudio,
} from '@pipecat-ai/client-react';
import { RTVIClient } from '@pipecat-ai/client-js';
import { DailyTransport } from '@pipecat-ai/daily-transport';
import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// 1. Fixed AIVoiceInput dynamic import
const AIVoiceInput = dynamic(
  () => import('./ui/ai-voice-input').then(mod => mod.AIVoiceInput),
  {
    ssr: false,
    loading: () => <div>Loading microphone...</div>
  }
);

interface PipecatWidgetProps {
  agentId?: string;
  testMode?: boolean;
}

export function PipecatWidget({ agentId, testMode = false }: PipecatWidgetProps) {
  // Create client with agent-specific configuration
  const client = new RTVIClient({
    transport: new DailyTransport(),
    params: {
      baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:7860',
      endpoints: {
        connect: '/connect',
        action: '/action'
      }
    },
    enableMic: true,
    enableCam: false,
    customConnectHandler: agentId ? async (params, timeout, controller) => {
      try {
        const baseUrl = params.baseUrl || 'http://localhost:7860';
        const connectEndpoint = params.endpoints?.connect || '/connect';

        const response = await fetch(`${baseUrl}${connectEndpoint}?agent_id=${agentId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...params.headers
          },
          signal: controller.signal
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.detail || `HTTP error: ${response.status}`);
        }

        const { room_url, token } = await response.json();

        const transport = client.transport as DailyTransport;
        await transport.connect({ room_url, token }, controller);

        if (timeout) clearTimeout(timeout);
      } catch (error) {
        console.error('Connection failed:', error);
        controller.abort();
        throw error;
      }
    } : undefined
  });

  return (
    <RTVIClientProvider client={client}>
      <WidgetContent testMode={testMode} agentId={agentId} />
    </RTVIClientProvider>
  );
}

interface WidgetContentProps {
  testMode?: boolean;
  agentId?: string;
}

function WidgetContent({ testMode = false, agentId }: WidgetContentProps) {
  const client = useRTVIClient();
  const transportState = useRTVIClientTransportState();
  const [error, setError] = useState<string | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    return () => {
      client?.disconnect();
    };
  }, [client]);

  if (!isMounted) return null;

  const containerClass = testMode
    ? "flex flex-col items-center gap-4"
    : "fixed bottom-8 right-8 flex flex-col items-end gap-4";

  const handleToggle = async (isActive: boolean) => {
    try {
      if (isActive) {
        if (!agentId && !testMode) {
          setError('Please select an agent to start conversation');
          return;
        }
        await client?.connect();
      } else {
        await client?.disconnect();
      }
      setError(null);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Connection failed'
      );
    }
  };

  return (
    <div className={containerClass}>
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg">
        {error && (
          <div className="text-red-500 text-sm p-2">{error}</div>
        )}
        {!agentId && !testMode && (
          <div className="text-yellow-600 text-sm p-2">
            No agent selected. Please select an agent to start conversation.
          </div>
        )}
        <AIVoiceInput
          isActive={['connected', 'ready'].includes(transportState)}
          onChange={handleToggle}
          className="w-auto"
          demoMode={false}
          disabled={!agentId && !testMode}
        />
      </div>
      <RTVIClientAudio />
    </div>
  );
}