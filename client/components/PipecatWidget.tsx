'use client';

import {
  R<PERSON><PERSON>lient<PERSON>rovider,
  useRTVI<PERSON>lient,
  useRTVIClientTransportState,
  RTVIClientAudio,
} from '@pipecat-ai/client-react';
import { RTVIClient } from '@pipecat-ai/client-js';
import { DailyTransport } from '@pipecat-ai/daily-transport';
import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// 1. Fixed AIVoiceInput dynamic import
const AIVoiceInput = dynamic(
  () => import('./ui/ai-voice-input').then(mod => mod.AIVoiceInput),
  {
    ssr: false,
    loading: () => <div>Loading microphone...</div>
  }
);

interface PipecatWidgetProps {
  agentId?: string;
  testMode?: boolean;
}

export function PipecatWidget({ agentId, testMode = false }: PipecatWidgetProps) {
  // Create client with agent-specific configuration
  const client = new RTVIClient({
    transport: new DailyTransport(),
    params: {
      baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:7860',
      endpoints: {
        connect: '/connect',
        action: '/action'
      },
      requestData: agentId ? { agent_id: agentId } : undefined
    },
    enableMic: true,
    enableCam: false
  });

  return (
    <RTVIClientProvider client={client}>
      <WidgetContent testMode={testMode} />
    </RTVIClientProvider>
  );
}

interface WidgetContentProps {
  testMode?: boolean;
}

function WidgetContent({ testMode = false }: WidgetContentProps) {
  const client = useRTVIClient();
  const transportState = useRTVIClientTransportState();
  const [error, setError] = useState<string | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    return () => {
      client?.disconnect();
    };
  }, [client]);

  if (!isMounted) return null;

  const containerClass = testMode
    ? "flex flex-col items-center gap-4"
    : "fixed bottom-8 right-8 flex flex-col items-end gap-4";

  return (
    <div className={containerClass}>
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg">
        {error && (
          <div className="text-red-500 text-sm p-2">{error}</div>
        )}
        <AIVoiceInput
          isActive={['connected', 'ready'].includes(transportState)}
          onChange={async (isActive: boolean) => {
            try {
              if (isActive) {
                await client?.connect();
              } else {
                await client?.disconnect();
              }
              setError(null);
            } catch (err) {
              setError(
                err instanceof Error ? err.message : 'Connection failed'
              );
            }
          }}
          className="w-auto"
          demoMode={false}
        />
      </div>
      <RTVIClientAudio />
    </div>
  );
}